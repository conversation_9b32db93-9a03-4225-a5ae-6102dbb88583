{"AWSTemplateFormatVersion": "2010-09-09", "Description": "{\"createdOn\":\"Windows\",\"createdBy\":\"Amplify\",\"createdWith\":\"10.8.1\",\"stackType\":\"function-Lambda\",\"metadata\":{}}", "Parameters": {"env": {"Type": "String"}, "authtoppropertyb44b7e30UserPoolId": {"Type": "String", "Default": "authtoppropertyb44b7e30UserPoolId"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Metadata": {"aws:asset:path": "./src", "aws:asset:property": "Code"}, "Properties": {"Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "AdminQueriescf1cdd0c", {"Fn::Join": ["", ["AdminQueriescf1cdd0c", "-", {"Ref": "env"}]]}]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}, "GROUP": "server", "USERPOOL": {"Ref": "authtoppropertyb44b7e30UserPoolId"}}}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Runtime": "nodejs14.x", "Timeout": 25, "Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "AdminQueriescf1cdd0cLambdaRole", {"Fn::Join": ["", ["AdminQueriescf1cdd0cLambdaRole", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}}}, "lambdaexecutionpolicy": {"DependsOn": ["LambdaExecutionRole"], "Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lambda-execution-policy", "Roles": [{"Ref": "LambdaExecutionRole"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": {"Fn::Sub": ["arn:aws:logs:${region}:${account}:log-group:/aws/lambda/${lambda}:log-stream:*", {"region": {"Ref": "AWS::Region"}, "account": {"Ref": "AWS::AccountId"}, "lambda": {"Ref": "LambdaFunction"}}]}}, {"Effect": "Allow", "Action": ["cognito-idp:ListUsersInGroup", "cognito-idp:AdminUserGlobalSignOut", "cognito-idp:AdminEnableUser", "cognito-idp:AdminDisableUser", "cognito-idp:AdminRemoveUserFromGroup", "cognito-idp:AdminAddUserToGroup", "cognito-idp:AdminListGroupsForUser", "cognito-idp:AdminGetUser", "cognito-idp:AdminConfirmSignUp", "cognito-idp:ListUsers", "cognito-idp:ListGroups"], "Resource": {"Fn::Join": ["", ["arn:aws:cognito-idp:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":userpool/", {"Ref": "authtoppropertyb44b7e30UserPoolId"}]]}}]}}}}, "Outputs": {"Name": {"Value": {"Ref": "LambdaFunction"}}, "Arn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}, "Region": {"Value": {"Ref": "AWS::Region"}}, "LambdaExecutionRole": {"Value": {"Ref": "LambdaExecutionRole"}}, "LambdaExecutionRoleArn": {"Value": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}}}}