{"maritalStatus": {"Divorced": 82, "Married": 213, "Never married": 183, "Separated": 28, "Total": 552, "Widowed": 43}, "averagesByPostCode": {"Average household size": 2.1, "Average number of persons per bedroom": 0.8, "Median age of persons": 53, "Median mortgage repayment ($/monthly)": 691, "Median rent ($/weekly)": 173, "Median total family income ($/weekly)": 1105, "Median total household income ($/weekly)": 775, "Median total personal income ($/weekly)": 462}, "populationByPostCode": {"0-4 years / Females": 8, "0-4 years / Males": 10, "0-4 years / Persons": 19, "10-14 years / Females": 19, "10-14 years / Males": 20, "10-14 years / Persons": 36, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 13, "15-19 years / Males": 20, "15-19 years / Persons": 28, "20-24 years / Females": 11, "20-24 years / Males": 16, "20-24 years / Persons": 29, "25-29 years / Females": 12, "25-29 years / Males": 9, "25-29 years / Persons": 19, "30-34 years / Females": 6, "30-34 years / Males": 11, "30-34 years / Persons": 20, "35-39 years / Females": 15, "35-39 years / Males": 17, "35-39 years / Persons": 35, "40-44 years / Females": 22, "40-44 years / Males": 18, "40-44 years / Persons": 32, "45-49 years / Females": 26, "45-49 years / Males": 20, "45-49 years / Persons": 36, "5-9 years / Females": 6, "5-9 years / Males": 15, "5-9 years / Persons": 25, "50-54 years / Females": 31, "50-54 years / Males": 23, "50-54 years / Persons": 49, "55-59 years / Females": 27, "55-59 years / Males": 22, "55-59 years / Persons": 51, "60-64 years / Females": 33, "60-64 years / Males": 35, "60-64 years / Persons": 69, "65-69 years / Females": 34, "65-69 years / Males": 40, "65-69 years / Persons": 75, "70-74 years / Females": 18, "70-74 years / Males": 33, "70-74 years / Persons": 53, "75-79 years / Females": 8, "75-79 years / Males": 18, "75-79 years / Persons": 29, "80-84 years / Females": 9, "80-84 years / Males": 6, "80-84 years / Persons": 15, "85-89 years / Females": 6, "85-89 years / Males": 3, "85-89 years / Persons": 9, "90-94 years / Females": 0, "90-94 years / Males": 4, "90-94 years / Persons": 6, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "houseHoldTypes": {"Other tenure type": 12, "Owned outright": 158, "Owned with a mortgage": 63, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 10, "Rented: Person not in same household": 18, "Rented: Real estate agent": 8, "Rented: State or territory housing authority": 9, "Rented: Total": 39, "Tenure type not stated": 7, "Total": 275}, "dwellingData": {"postCode": "7214", "Total_NofB_0_i_b": "14", "Total_NofB_1": "18", "Total_NofB_2": "44", "Total_NofB_3": "144", "Total_NofB_4": "36", "Total_NofB_5": "7", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "10", "Total_Total": "275"}, "healthData": {"postCode": "7214", "P_0_cond_Tot": "307", "P_1m_cond_Tot_Tot": "256", "P_2_cond_Tot": "67", "P_3_or_mo_cond_Tot": "44", "P_cond_NS_Tot": "65", "P_Tot_Tot": "630"}, "families": {"postCode": "7214", "over_65": 187, "Tot_P": 630, "CF_ChU15_a_Total_F": 31, "OPF_ChU15_a_Total_F": 11, "Total_F": 168}}