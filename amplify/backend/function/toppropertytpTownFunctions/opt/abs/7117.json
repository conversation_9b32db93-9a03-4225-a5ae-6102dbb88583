{"populationByPostCode": {"0-4 years / Females": 19, "0-4 years / Males": 11, "0-4 years / Persons": 27, "10-14 years / Females": 24, "10-14 years / Males": 20, "10-14 years / Persons": 40, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 10, "15-19 years / Males": 24, "15-19 years / Persons": 34, "20-24 years / Females": 10, "20-24 years / Males": 14, "20-24 years / Persons": 24, "25-29 years / Females": 19, "25-29 years / Males": 21, "25-29 years / Persons": 40, "30-34 years / Females": 22, "30-34 years / Males": 17, "30-34 years / Persons": 40, "35-39 years / Females": 18, "35-39 years / Males": 28, "35-39 years / Persons": 41, "40-44 years / Females": 17, "40-44 years / Males": 21, "40-44 years / Persons": 39, "45-49 years / Females": 25, "45-49 years / Males": 28, "45-49 years / Persons": 49, "5-9 years / Females": 17, "5-9 years / Males": 25, "5-9 years / Persons": 42, "50-54 years / Females": 37, "50-54 years / Males": 32, "50-54 years / Persons": 69, "55-59 years / Females": 46, "55-59 years / Males": 41, "55-59 years / Persons": 92, "60-64 years / Females": 42, "60-64 years / Males": 53, "60-64 years / Persons": 101, "65-69 years / Females": 47, "65-69 years / Males": 27, "65-69 years / Persons": 74, "70-74 years / Females": 39, "70-74 years / Males": 49, "70-74 years / Persons": 90, "75-79 years / Females": 25, "75-79 years / Males": 38, "75-79 years / Persons": 60, "80-84 years / Females": 13, "80-84 years / Males": 21, "80-84 years / Persons": 37, "85-89 years / Females": 10, "85-89 years / Males": 6, "85-89 years / Persons": 19, "90-94 years / Females": 3, "90-94 years / Males": 4, "90-94 years / Persons": 4, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "maritalStatus": {"Divorced": 111, "Married": 363, "Never married": 251, "Separated": 28, "Total": 811, "Widowed": 66}, "averagesByPostCode": {"Average household size": 2.1, "Average number of persons per bedroom": 0.8, "Median age of persons": 55, "Median mortgage repayment ($/monthly)": 1170, "Median rent ($/weekly)": 295, "Median total family income ($/weekly)": 1434, "Median total household income ($/weekly)": 962, "Median total personal income ($/weekly)": 501}, "houseHoldTypes": {"Other tenure type": 15, "Owned outright": 210, "Owned with a mortgage": 115, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 13, "Rented: Person not in same household": 31, "Rented: Real estate agent": 18, "Rented: State or territory housing authority": 0, "Rented: Total": 55, "Tenure type not stated": 12, "Total": 398}, "dwellingData": {"postCode": "7117", "Total_NofB_0_i_b": "6", "Total_NofB_1": "25", "Total_NofB_2": "98", "Total_NofB_3": "212", "Total_NofB_4": "44", "Total_NofB_5": "11", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "9", "Total_Total": "398"}, "healthData": {"postCode": "7117", "P_0_cond_Tot": "501", "P_1m_cond_Tot_Tot": "343", "P_2_cond_Tot": "82", "P_3_or_mo_cond_Tot": "46", "P_cond_NS_Tot": "76", "P_Tot_Tot": "923"}, "families": {"postCode": "7117", "over_65": 284, "Tot_P": 923, "CF_ChU15_a_Total_F": 50, "OPF_ChU15_a_Total_F": 13, "Total_F": 249}}