{"maritalStatus": {"Divorced": 113, "Married": 706, "Never married": 501, "Separated": 40, "Total": 1419, "Widowed": 62}, "averagesByPostCode": {"Average household size": 2.5, "Average number of persons per bedroom": 0.8, "Median age of persons": 40, "Median mortgage repayment ($/monthly)": 1220, "Median rent ($/weekly)": 220, "Median total family income ($/weekly)": 1602, "Median total household income ($/weekly)": 1247, "Median total personal income ($/weekly)": 705}, "populationByPostCode": {"0-4 years / Females": 42, "0-4 years / Males": 55, "0-4 years / Persons": 100, "10-14 years / Females": 63, "10-14 years / Males": 67, "10-14 years / Persons": 131, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 51, "15-19 years / Males": 46, "15-19 years / Persons": 100, "20-24 years / Females": 44, "20-24 years / Males": 45, "20-24 years / Persons": 88, "25-29 years / Females": 54, "25-29 years / Males": 56, "25-29 years / Persons": 108, "30-34 years / Females": 56, "30-34 years / Males": 62, "30-34 years / Persons": 117, "35-39 years / Females": 62, "35-39 years / Males": 61, "35-39 years / Persons": 122, "40-44 years / Females": 59, "40-44 years / Males": 44, "40-44 years / Persons": 99, "45-49 years / Females": 50, "45-49 years / Males": 55, "45-49 years / Persons": 103, "5-9 years / Females": 47, "5-9 years / Males": 57, "5-9 years / Persons": 106, "50-54 years / Females": 65, "50-54 years / Males": 62, "50-54 years / Persons": 132, "55-59 years / Females": 56, "55-59 years / Males": 58, "55-59 years / Persons": 118, "60-64 years / Females": 63, "60-64 years / Males": 83, "60-64 years / Persons": 143, "65-69 years / Females": 39, "65-69 years / Males": 59, "65-69 years / Persons": 101, "70-74 years / Females": 43, "70-74 years / Males": 35, "70-74 years / Persons": 83, "75-79 years / Females": 27, "75-79 years / Males": 34, "75-79 years / Persons": 63, "80-84 years / Females": 10, "80-84 years / Males": 12, "80-84 years / Persons": 20, "85-89 years / Females": 9, "85-89 years / Males": 6, "85-89 years / Persons": 20, "90-94 years / Females": 8, "90-94 years / Males": 6, "90-94 years / Persons": 11, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "houseHoldTypes": {"Other tenure type": 34, "Owned outright": 244, "Owned with a mortgage": 227, "Rented: Community housing provider": 4, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 34, "Rented: Person not in same household": 78, "Rented: Real estate agent": 27, "Rented: State or territory housing authority": 4, "Rented: Total": 141, "Tenure type not stated": 18, "Total": 661}, "dwellingData": {"postCode": "7302", "Total_NofB_0_i_b": "3", "Total_NofB_1": "8", "Total_NofB_2": "90", "Total_NofB_3": "378", "Total_NofB_4": "136", "Total_NofB_5": "18", "Total_NofB_6_or_m": "10", "Total_NofB_NS": "15", "Total_Total": "661"}, "healthData": {"postCode": "7302", "P_0_cond_Tot": "1083", "P_1m_cond_Tot_Tot": "517", "P_2_cond_Tot": "113", "P_3_or_mo_cond_Tot": "54", "P_cond_NS_Tot": "154", "P_Tot_Tot": "1753"}, "families": {"postCode": "7302", "over_65": 298, "Tot_P": 1753, "CF_ChU15_a_Total_F": 135, "OPF_ChU15_a_Total_F": 46, "Total_F": 477}}