{"maritalStatus": {"Divorced": 45, "Married": 217, "Never married": 150, "Separated": 27, "Total": 460, "Widowed": 18}, "averagesByPostCode": {"Average household size": 2.4, "Average number of persons per bedroom": 0.8, "Median age of persons": 47, "Median mortgage repayment ($/monthly)": 1300, "Median rent ($/weekly)": 250, "Median total family income ($/weekly)": 1460, "Median total household income ($/weekly)": 1283, "Median total personal income ($/weekly)": 583}, "houseHoldTypes": {"Other tenure type": 10, "Owned outright": 102, "Owned with a mortgage": 72, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 5, "Rented: Person not in same household": 14, "Rented: Real estate agent": 0, "Rented: State or territory housing authority": 0, "Rented: Total": 23, "Tenure type not stated": 0, "Total": 207}, "populationByPostCode": {"0-4 years / Females": 23, "0-4 years / Males": 13, "0-4 years / Persons": 35, "10-14 years / Females": 21, "10-14 years / Males": 23, "10-14 years / Persons": 43, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 10, "15-19 years / Males": 15, "15-19 years / Persons": 24, "20-24 years / Females": 3, "20-24 years / Males": 11, "20-24 years / Persons": 19, "25-29 years / Females": 8, "25-29 years / Males": 17, "25-29 years / Persons": 21, "30-34 years / Females": 19, "30-34 years / Males": 18, "30-34 years / Persons": 32, "35-39 years / Females": 9, "35-39 years / Males": 17, "35-39 years / Persons": 25, "40-44 years / Females": 20, "40-44 years / Males": 18, "40-44 years / Persons": 31, "45-49 years / Females": 16, "45-49 years / Males": 33, "45-49 years / Persons": 49, "5-9 years / Females": 17, "5-9 years / Males": 15, "5-9 years / Persons": 30, "50-54 years / Females": 21, "50-54 years / Males": 19, "50-54 years / Persons": 41, "55-59 years / Females": 22, "55-59 years / Males": 41, "55-59 years / Persons": 59, "60-64 years / Females": 24, "60-64 years / Males": 29, "60-64 years / Persons": 55, "65-69 years / Females": 19, "65-69 years / Males": 25, "65-69 years / Persons": 40, "70-74 years / Females": 8, "70-74 years / Males": 22, "70-74 years / Persons": 34, "75-79 years / Females": 9, "75-79 years / Males": 6, "75-79 years / Persons": 16, "80-84 years / Females": 0, "80-84 years / Males": 3, "80-84 years / Persons": 12, "85-89 years / Females": 5, "85-89 years / Males": 4, "85-89 years / Persons": 8, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "dwellingData": {"postCode": "7254", "Total_NofB_0_i_b": "0", "Total_NofB_1": "10", "Total_NofB_2": "34", "Total_NofB_3": "112", "Total_NofB_4": "42", "Total_NofB_5": "8", "Total_NofB_6_or_m": "4", "Total_NofB_NS": "0", "Total_Total": "207"}, "healthData": {"postCode": "7254", "P_0_cond_Tot": "334", "P_1m_cond_Tot_Tot": "164", "P_2_cond_Tot": "51", "P_3_or_mo_cond_Tot": "28", "P_cond_NS_Tot": "64", "P_Tot_Tot": "562"}, "families": {"postCode": "7254", "over_65": 110, "Tot_P": 562, "CF_ChU15_a_Total_F": 37, "OPF_ChU15_a_Total_F": 13, "Total_F": 155}}