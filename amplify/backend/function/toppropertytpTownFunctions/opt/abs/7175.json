{"houseHoldTypes": {"Other tenure type": 3, "Owned outright": 41, "Owned with a mortgage": 17, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 0, "Rented: Person not in same household": 8, "Rented: Real estate agent": 0, "Rented: State or territory housing authority": 0, "Rented: Total": 15, "Tenure type not stated": 3, "Total": 73}, "populationByPostCode": {"0-4 years / Females": 3, "0-4 years / Males": 4, "0-4 years / Persons": 8, "10-14 years / Females": 4, "10-14 years / Males": 5, "10-14 years / Persons": 13, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 3, "15-19 years / Males": 4, "15-19 years / Persons": 3, "20-24 years / Females": 7, "20-24 years / Males": 8, "20-24 years / Persons": 13, "25-29 years / Females": 0, "25-29 years / Males": 13, "25-29 years / Persons": 13, "30-34 years / Females": 7, "30-34 years / Males": 5, "30-34 years / Persons": 8, "35-39 years / Females": 4, "35-39 years / Males": 7, "35-39 years / Persons": 9, "40-44 years / Females": 5, "40-44 years / Males": 3, "40-44 years / Persons": 6, "45-49 years / Females": 9, "45-49 years / Males": 5, "45-49 years / Persons": 17, "5-9 years / Females": 4, "5-9 years / Males": 5, "5-9 years / Persons": 6, "50-54 years / Females": 0, "50-54 years / Males": 9, "50-54 years / Persons": 8, "55-59 years / Females": 8, "55-59 years / Males": 12, "55-59 years / Persons": 14, "60-64 years / Females": 11, "60-64 years / Males": 7, "60-64 years / Persons": 22, "65-69 years / Females": 3, "65-69 years / Males": 6, "65-69 years / Persons": 18, "70-74 years / Females": 6, "70-74 years / Males": 9, "70-74 years / Persons": 7, "75-79 years / Females": 6, "75-79 years / Males": 3, "75-79 years / Persons": 4, "80-84 years / Females": 3, "80-84 years / Males": 5, "80-84 years / Persons": 5, "85-89 years / Females": 0, "85-89 years / Males": 0, "85-89 years / Persons": 0, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "maritalStatus": {"Divorced": 23, "Married": 59, "Never married": 60, "Separated": 6, "Total": 154, "Widowed": 10}, "averagesByPostCode": {"Average household size": 2.2, "Average number of persons per bedroom": 0.8, "Median age of persons": 48, "Median mortgage repayment ($/monthly)": 1343, "Median rent ($/weekly)": 260, "Median total family income ($/weekly)": 1949, "Median total household income ($/weekly)": 1300, "Median total personal income ($/weekly)": 778}, "dwellingData": {"postCode": "7175", "Total_NofB_0_i_b": "0", "Total_NofB_1": "3", "Total_NofB_2": "21", "Total_NofB_3": "29", "Total_NofB_4": "13", "Total_NofB_5": "6", "Total_NofB_6_or_m": "4", "Total_NofB_NS": "3", "Total_Total": "73"}, "healthData": {"postCode": "7175", "P_0_cond_Tot": "111", "P_1m_cond_Tot_Tot": "61", "P_2_cond_Tot": "11", "P_3_or_mo_cond_Tot": "10", "P_cond_NS_Tot": "12", "P_Tot_Tot": "184"}, "families": {"postCode": "7175", "over_65": 34, "Tot_P": 184, "CF_ChU15_a_Total_F": 14, "OPF_ChU15_a_Total_F": 4, "Total_F": 48}}