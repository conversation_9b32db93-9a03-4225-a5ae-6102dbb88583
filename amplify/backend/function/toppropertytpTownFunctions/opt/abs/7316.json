{"maritalStatus": {"Divorced": 585, "Married": 2601, "Never married": 1535, "Separated": 188, "Total": 5241, "Widowed": 335}, "populationByPostCode": {"0-4 years / Females": 138, "0-4 years / Males": 158, "0-4 years / Persons": 290, "10-14 years / Females": 182, "10-14 years / Males": 187, "10-14 years / Persons": 366, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 173, "15-19 years / Males": 185, "15-19 years / Persons": 358, "20-24 years / Females": 144, "20-24 years / Males": 146, "20-24 years / Persons": 283, "25-29 years / Females": 157, "25-29 years / Males": 159, "25-29 years / Persons": 315, "30-34 years / Females": 212, "30-34 years / Males": 156, "30-34 years / Persons": 364, "35-39 years / Females": 178, "35-39 years / Males": 158, "35-39 years / Persons": 336, "40-44 years / Females": 161, "40-44 years / Males": 154, "40-44 years / Persons": 320, "45-49 years / Females": 209, "45-49 years / Males": 210, "45-49 years / Persons": 414, "5-9 years / Females": 187, "5-9 years / Males": 199, "5-9 years / Persons": 387, "50-54 years / Females": 251, "50-54 years / Males": 184, "50-54 years / Persons": 439, "55-59 years / Females": 250, "55-59 years / Males": 230, "55-59 years / Persons": 487, "60-64 years / Females": 262, "60-64 years / Males": 275, "60-64 years / Persons": 536, "65-69 years / Females": 231, "65-69 years / Males": 208, "65-69 years / Persons": 437, "70-74 years / Females": 177, "70-74 years / Males": 206, "70-74 years / Persons": 380, "75-79 years / Females": 126, "75-79 years / Males": 128, "75-79 years / Persons": 254, "80-84 years / Females": 99, "80-84 years / Males": 81, "80-84 years / Persons": 175, "85-89 years / Females": 53, "85-89 years / Males": 29, "85-89 years / Persons": 80, "90-94 years / Females": 36, "90-94 years / Males": 14, "90-94 years / Persons": 50, "95-99 years / Females": 10, "95-99 years / Males": 3, "95-99 years / Persons": 10}, "averagesByPostCode": {"Average household size": 2.4, "Average number of persons per bedroom": 0.8, "Median age of persons": 46, "Median mortgage repayment ($/monthly)": 1300, "Median rent ($/weekly)": 250, "Median total family income ($/weekly)": 1742, "Median total household income ($/weekly)": 1362, "Median total personal income ($/weekly)": 681}, "houseHoldTypes": {"Other tenure type": 41, "Owned outright": 1036, "Owned with a mortgage": 913, "Rented: Community housing provider": 5, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 18, "Rented: Person not in same household": 152, "Rented: Real estate agent": 192, "Rented: State or territory housing authority": 63, "Rented: Total": 434, "Tenure type not stated": 63, "Total": 2490}, "dwellingData": {"postCode": "7316", "Total_NofB_0_i_b": "3", "Total_NofB_1": "92", "Total_NofB_2": "324", "Total_NofB_3": "1366", "Total_NofB_4": "534", "Total_NofB_5": "92", "Total_NofB_6_or_m": "15", "Total_NofB_NS": "57", "Total_Total": "2490"}, "healthData": {"postCode": "7316", "P_0_cond_Tot": "3687", "P_1m_cond_Tot_Tot": "2126", "P_2_cond_Tot": "491", "P_3_or_mo_cond_Tot": "266", "P_cond_NS_Tot": "475", "P_Tot_Tot": "6285"}, "families": {"postCode": "7316", "over_65": 1386, "Tot_P": 6285, "CF_ChU15_a_Total_F": 448, "OPF_ChU15_a_Total_F": 119, "Total_F": 1795}}