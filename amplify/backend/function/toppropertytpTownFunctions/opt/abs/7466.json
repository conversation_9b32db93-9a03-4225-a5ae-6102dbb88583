{"maritalStatus": {"Divorced": 7, "Married": 10, "Never married": 6, "Separated": 4, "Total": 25, "Widowed": 0}, "populationByPostCode": {"0-4 years / Females": 0, "0-4 years / Males": 0, "0-4 years / Persons": 0, "10-14 years / Females": 3, "10-14 years / Males": 0, "10-14 years / Persons": 3, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 0, "15-19 years / Males": 0, "15-19 years / Persons": 0, "20-24 years / Females": 0, "20-24 years / Males": 0, "20-24 years / Persons": 0, "25-29 years / Females": 0, "25-29 years / Males": 0, "25-29 years / Persons": 0, "30-34 years / Females": 0, "30-34 years / Males": 3, "30-34 years / Persons": 3, "35-39 years / Females": 0, "35-39 years / Males": 0, "35-39 years / Persons": 0, "40-44 years / Females": 0, "40-44 years / Males": 0, "40-44 years / Persons": 0, "45-49 years / Females": 0, "45-49 years / Males": 0, "45-49 years / Persons": 0, "5-9 years / Females": 0, "5-9 years / Males": 0, "5-9 years / Persons": 0, "50-54 years / Females": 0, "50-54 years / Males": 0, "50-54 years / Persons": 4, "55-59 years / Females": 0, "55-59 years / Males": 0, "55-59 years / Persons": 0, "60-64 years / Females": 0, "60-64 years / Males": 0, "60-64 years / Persons": 3, "65-69 years / Females": 3, "65-69 years / Males": 5, "65-69 years / Persons": 7, "70-74 years / Females": 0, "70-74 years / Males": 4, "70-74 years / Persons": 8, "75-79 years / Females": 0, "75-79 years / Males": 0, "75-79 years / Persons": 0, "80-84 years / Females": 0, "80-84 years / Males": 0, "80-84 years / Persons": 0, "85-89 years / Females": 0, "85-89 years / Males": 0, "85-89 years / Persons": 0, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 2.2, "Average number of persons per bedroom": 0.9, "Median age of persons": 59, "Median mortgage repayment ($/monthly)": 417, "Median rent ($/weekly)": 46, "Median total family income ($/weekly)": 900, "Median total household income ($/weekly)": 875, "Median total personal income ($/weekly)": 417}, "houseHoldTypes": {"Other tenure type": 0, "Owned outright": 8, "Owned with a mortgage": 4, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 0, "Rented: Person not in same household": 0, "Rented: Real estate agent": 0, "Rented: State or territory housing authority": 0, "Rented: Total": 0, "Tenure type not stated": 0, "Total": 16}, "dwellingData": {"postCode": "7466", "Total_NofB_0_i_b": "0", "Total_NofB_1": "0", "Total_NofB_2": "5", "Total_NofB_3": "6", "Total_NofB_4": "0", "Total_NofB_5": "0", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "0", "Total_Total": "16"}, "healthData": {"postCode": "7466", "P_0_cond_Tot": "18", "P_1m_cond_Tot_Tot": "15", "P_2_cond_Tot": "0", "P_3_or_mo_cond_Tot": "0", "P_cond_NS_Tot": "0", "P_Tot_Tot": "32"}, "families": {"postCode": "7466", "over_65": 15, "Tot_P": 32, "CF_ChU15_a_Total_F": 0, "OPF_ChU15_a_Total_F": 0, "Total_F": 9}}