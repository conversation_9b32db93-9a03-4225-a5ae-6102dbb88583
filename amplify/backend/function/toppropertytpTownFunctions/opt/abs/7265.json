{"populationByPostCode": {"0-4 years / Females": 3, "0-4 years / Males": 11, "0-4 years / Persons": 12, "10-14 years / Females": 16, "10-14 years / Males": 6, "10-14 years / Persons": 22, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 3, "15-19 years / Males": 3, "15-19 years / Persons": 8, "20-24 years / Females": 0, "20-24 years / Males": 0, "20-24 years / Persons": 3, "25-29 years / Females": 6, "25-29 years / Males": 5, "25-29 years / Persons": 12, "30-34 years / Females": 10, "30-34 years / Males": 10, "30-34 years / Persons": 11, "35-39 years / Females": 3, "35-39 years / Males": 10, "35-39 years / Persons": 15, "40-44 years / Females": 10, "40-44 years / Males": 4, "40-44 years / Persons": 11, "45-49 years / Females": 3, "45-49 years / Males": 5, "45-49 years / Persons": 16, "5-9 years / Females": 5, "5-9 years / Males": 4, "5-9 years / Persons": 8, "50-54 years / Females": 8, "50-54 years / Males": 4, "50-54 years / Persons": 15, "55-59 years / Females": 10, "55-59 years / Males": 6, "55-59 years / Persons": 12, "60-64 years / Females": 14, "60-64 years / Males": 27, "60-64 years / Persons": 35, "65-69 years / Females": 11, "65-69 years / Males": 5, "65-69 years / Persons": 11, "70-74 years / Females": 5, "70-74 years / Males": 12, "70-74 years / Persons": 15, "75-79 years / Females": 7, "75-79 years / Males": 0, "75-79 years / Persons": 8, "80-84 years / Females": 3, "80-84 years / Males": 0, "80-84 years / Persons": 6, "85-89 years / Females": 0, "85-89 years / Males": 0, "85-89 years / Persons": 0, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 2.1, "Average number of persons per bedroom": 0.7, "Median age of persons": 48, "Median mortgage repayment ($/monthly)": 492, "Median rent ($/weekly)": 226, "Median total family income ($/weekly)": 1031, "Median total household income ($/weekly)": 927, "Median total personal income ($/weekly)": 488}, "houseHoldTypes": {"Other tenure type": 7, "Owned outright": 50, "Owned with a mortgage": 25, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 4, "Rented: Person not in same household": 4, "Rented: Real estate agent": 3, "Rented: State or territory housing authority": 0, "Rented: Total": 12, "Tenure type not stated": 0, "Total": 95}, "maritalStatus": {"Divorced": 24, "Married": 98, "Never married": 52, "Separated": 3, "Total": 185, "Widowed": 15}, "dwellingData": {"postCode": "7265", "Total_NofB_0_i_b": "0", "Total_NofB_1": "3", "Total_NofB_2": "16", "Total_NofB_3": "54", "Total_NofB_4": "19", "Total_NofB_5": "5", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "0", "Total_Total": "95"}, "healthData": {"postCode": "7265", "P_0_cond_Tot": "132", "P_1m_cond_Tot_Tot": "78", "P_2_cond_Tot": "18", "P_3_or_mo_cond_Tot": "11", "P_cond_NS_Tot": "20", "P_Tot_Tot": "233"}, "families": {"postCode": "7265", "over_65": 40, "Tot_P": 233, "CF_ChU15_a_Total_F": 9, "OPF_ChU15_a_Total_F": 7, "Total_F": 65}}