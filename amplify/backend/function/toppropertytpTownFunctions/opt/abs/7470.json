{"maritalStatus": {"Divorced": 91, "Married": 230, "Never married": 256, "Separated": 44, "Total": 651, "Widowed": 31}, "averagesByPostCode": {"Average household size": 1.9, "Average number of persons per bedroom": 0.7, "Median age of persons": 48, "Median mortgage repayment ($/monthly)": 600, "Median rent ($/weekly)": 150, "Median total family income ($/weekly)": 1285, "Median total household income ($/weekly)": 962, "Median total personal income ($/weekly)": 524}, "houseHoldTypes": {"Other tenure type": 19, "Owned outright": 153, "Owned with a mortgage": 61, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 25, "Rented: Person not in same household": 12, "Rented: Real estate agent": 43, "Rented: State or territory housing authority": 4, "Rented: Total": 89, "Tenure type not stated": 4, "Total": 324}, "populationByPostCode": {"0-4 years / Females": 17, "0-4 years / Males": 6, "0-4 years / Persons": 18, "10-14 years / Females": 21, "10-14 years / Males": 23, "10-14 years / Persons": 43, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 20, "15-19 years / Males": 14, "15-19 years / Persons": 30, "20-24 years / Females": 18, "20-24 years / Males": 19, "20-24 years / Persons": 44, "25-29 years / Females": 12, "25-29 years / Males": 21, "25-29 years / Persons": 34, "30-34 years / Females": 27, "30-34 years / Males": 17, "30-34 years / Persons": 42, "35-39 years / Females": 27, "35-39 years / Males": 25, "35-39 years / Persons": 54, "40-44 years / Females": 20, "40-44 years / Males": 14, "40-44 years / Persons": 36, "45-49 years / Females": 23, "45-49 years / Males": 34, "45-49 years / Persons": 60, "5-9 years / Females": 20, "5-9 years / Males": 20, "5-9 years / Persons": 36, "50-54 years / Females": 20, "50-54 years / Males": 32, "50-54 years / Persons": 55, "55-59 years / Females": 42, "55-59 years / Males": 36, "55-59 years / Persons": 78, "60-64 years / Females": 26, "60-64 years / Males": 45, "60-64 years / Persons": 72, "65-69 years / Females": 34, "65-69 years / Males": 36, "65-69 years / Persons": 68, "70-74 years / Females": 12, "70-74 years / Males": 24, "70-74 years / Persons": 33, "75-79 years / Females": 5, "75-79 years / Males": 17, "75-79 years / Persons": 23, "80-84 years / Females": 9, "80-84 years / Males": 9, "80-84 years / Persons": 16, "85-89 years / Females": 7, "85-89 years / Males": 0, "85-89 years / Persons": 7, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "dwellingData": {"postCode": "7470", "Total_NofB_0_i_b": "7", "Total_NofB_1": "10", "Total_NofB_2": "55", "Total_NofB_3": "226", "Total_NofB_4": "25", "Total_NofB_5": "3", "Total_NofB_6_or_m": "3", "Total_NofB_NS": "5", "Total_Total": "324"}, "healthData": {"postCode": "7470", "P_0_cond_Tot": "353", "P_1m_cond_Tot_Tot": "285", "P_2_cond_Tot": "72", "P_3_or_mo_cond_Tot": "55", "P_cond_NS_Tot": "113", "P_Tot_Tot": "752"}, "families": {"postCode": "7470", "over_65": 147, "Tot_P": 752, "CF_ChU15_a_Total_F": 34, "OPF_ChU15_a_Total_F": 16, "Total_F": 166}}