{"populationByPostCode": {"0-4 years / Females": 3, "0-4 years / Males": 7, "0-4 years / Persons": 7, "10-14 years / Females": 5, "10-14 years / Males": 3, "10-14 years / Persons": 8, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 10, "15-19 years / Males": 4, "15-19 years / Persons": 14, "20-24 years / Females": 0, "20-24 years / Males": 3, "20-24 years / Persons": 5, "25-29 years / Females": 5, "25-29 years / Males": 6, "25-29 years / Persons": 8, "30-34 years / Females": 9, "30-34 years / Males": 12, "30-34 years / Persons": 19, "35-39 years / Females": 6, "35-39 years / Males": 7, "35-39 years / Persons": 15, "40-44 years / Females": 4, "40-44 years / Males": 5, "40-44 years / Persons": 12, "45-49 years / Females": 3, "45-49 years / Males": 8, "45-49 years / Persons": 8, "5-9 years / Females": 0, "5-9 years / Males": 0, "5-9 years / Persons": 0, "50-54 years / Females": 0, "50-54 years / Males": 5, "50-54 years / Persons": 5, "55-59 years / Females": 17, "55-59 years / Males": 9, "55-59 years / Persons": 28, "60-64 years / Females": 13, "60-64 years / Males": 6, "60-64 years / Persons": 23, "65-69 years / Females": 14, "65-69 years / Males": 7, "65-69 years / Persons": 23, "70-74 years / Females": 10, "70-74 years / Males": 10, "70-74 years / Persons": 28, "75-79 years / Females": 9, "75-79 years / Males": 11, "75-79 years / Persons": 17, "80-84 years / Females": 3, "80-84 years / Males": 6, "80-84 years / Persons": 13, "85-89 years / Females": 0, "85-89 years / Males": 3, "85-89 years / Persons": 9, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 1.9, "Average number of persons per bedroom": 0.7, "Median age of persons": 57, "Median mortgage repayment ($/monthly)": 975, "Median rent ($/weekly)": 200, "Median total family income ($/weekly)": 1187, "Median total household income ($/weekly)": 912, "Median total personal income ($/weekly)": 527}, "houseHoldTypes": {"Other tenure type": 5, "Owned outright": 57, "Owned with a mortgage": 20, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 4, "Rented: Person not in same household": 4, "Rented: Real estate agent": 8, "Rented: State or territory housing authority": 0, "Rented: Total": 16, "Tenure type not stated": 8, "Total": 107}, "maritalStatus": {"Divorced": 28, "Married": 102, "Never married": 66, "Separated": 9, "Total": 221, "Widowed": 16}, "dwellingData": {"postCode": "7182", "Total_NofB_0_i_b": "0", "Total_NofB_1": "6", "Total_NofB_2": "28", "Total_NofB_3": "53", "Total_NofB_4": "11", "Total_NofB_5": "0", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "3", "Total_Total": "107"}, "healthData": {"postCode": "7182", "P_0_cond_Tot": "119", "P_1m_cond_Tot_Tot": "103", "P_2_cond_Tot": "22", "P_3_or_mo_cond_Tot": "15", "P_cond_NS_Tot": "20", "P_Tot_Tot": "247"}, "families": {"postCode": "7182", "over_65": 90, "Tot_P": 247, "CF_ChU15_a_Total_F": 7, "OPF_ChU15_a_Total_F": 0, "Total_F": 62}}