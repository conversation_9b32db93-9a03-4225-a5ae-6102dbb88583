{"populationByPostCode": {"0-4 years / Females": 11, "0-4 years / Males": 7, "0-4 years / Persons": 18, "10-14 years / Females": 7, "10-14 years / Males": 8, "10-14 years / Persons": 18, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 7, "15-19 years / Males": 10, "15-19 years / Persons": 15, "20-24 years / Females": 5, "20-24 years / Males": 3, "20-24 years / Persons": 10, "25-29 years / Females": 9, "25-29 years / Males": 0, "25-29 years / Persons": 11, "30-34 years / Females": 9, "30-34 years / Males": 8, "30-34 years / Persons": 14, "35-39 years / Females": 12, "35-39 years / Males": 15, "35-39 years / Persons": 27, "40-44 years / Females": 19, "40-44 years / Males": 8, "40-44 years / Persons": 28, "45-49 years / Females": 6, "45-49 years / Males": 16, "45-49 years / Persons": 21, "5-9 years / Females": 7, "5-9 years / Males": 16, "5-9 years / Persons": 27, "50-54 years / Females": 16, "50-54 years / Males": 16, "50-54 years / Persons": 39, "55-59 years / Females": 16, "55-59 years / Males": 12, "55-59 years / Persons": 24, "60-64 years / Females": 15, "60-64 years / Males": 22, "60-64 years / Persons": 41, "65-69 years / Females": 15, "65-69 years / Males": 16, "65-69 years / Persons": 31, "70-74 years / Females": 12, "70-74 years / Males": 23, "70-74 years / Persons": 34, "75-79 years / Females": 11, "75-79 years / Males": 9, "75-79 years / Persons": 20, "80-84 years / Females": 4, "80-84 years / Males": 9, "80-84 years / Persons": 11, "85-89 years / Females": 0, "85-89 years / Males": 3, "85-89 years / Persons": 3, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "maritalStatus": {"Divorced": 40, "Married": 172, "Never married": 108, "Separated": 8, "Total": 333, "Widowed": 16}, "averagesByPostCode": {"Average household size": 2.4, "Average number of persons per bedroom": 0.9, "Median age of persons": 51, "Median mortgage repayment ($/monthly)": 1375, "Median rent ($/weekly)": 300, "Median total family income ($/weekly)": 1525, "Median total household income ($/weekly)": 1397, "Median total personal income ($/weekly)": 637}, "houseHoldTypes": {"Other tenure type": 4, "Owned outright": 71, "Owned with a mortgage": 58, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 0, "Rented: Person not in same household": 3, "Rented: Real estate agent": 0, "Rented: State or territory housing authority": 0, "Rented: Total": 3, "Tenure type not stated": 5, "Total": 148}, "dwellingData": {"postCode": "7163", "Total_NofB_0_i_b": "0", "Total_NofB_1": "5", "Total_NofB_2": "41", "Total_NofB_3": "66", "Total_NofB_4": "22", "Total_NofB_5": "4", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "8", "Total_Total": "148"}, "healthData": {"postCode": "7163", "P_0_cond_Tot": "233", "P_1m_cond_Tot_Tot": "117", "P_2_cond_Tot": "25", "P_3_or_mo_cond_Tot": "16", "P_cond_NS_Tot": "47", "P_Tot_Tot": "397"}, "families": {"postCode": "7163", "over_65": 99, "Tot_P": 397, "CF_ChU15_a_Total_F": 25, "OPF_ChU15_a_Total_F": 4, "Total_F": 106}}