{"maritalStatus": {"Divorced": 51, "Married": 328, "Never married": 158, "Separated": 14, "Total": 569, "Widowed": 19}, "houseHoldTypes": {"Other tenure type": 3, "Owned outright": 153, "Owned with a mortgage": 79, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 5, "Rented: Person not in same household": 15, "Rented: Real estate agent": 4, "Rented: State or territory housing authority": 0, "Rented: Total": 27, "Tenure type not stated": 0, "Total": 263}, "populationByPostCode": {"0-4 years / Females": 10, "0-4 years / Males": 16, "0-4 years / Persons": 30, "10-14 years / Females": 14, "10-14 years / Males": 26, "10-14 years / Persons": 34, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 12, "15-19 years / Males": 20, "15-19 years / Persons": 26, "20-24 years / Females": 9, "20-24 years / Males": 16, "20-24 years / Persons": 18, "25-29 years / Females": 8, "25-29 years / Males": 14, "25-29 years / Persons": 16, "30-34 years / Females": 15, "30-34 years / Males": 9, "30-34 years / Persons": 22, "35-39 years / Females": 22, "35-39 years / Males": 11, "35-39 years / Persons": 36, "40-44 years / Females": 17, "40-44 years / Males": 21, "40-44 years / Persons": 37, "45-49 years / Females": 23, "45-49 years / Males": 19, "45-49 years / Persons": 43, "5-9 years / Females": 15, "5-9 years / Males": 22, "5-9 years / Persons": 33, "50-54 years / Females": 20, "50-54 years / Males": 10, "50-54 years / Persons": 33, "55-59 years / Females": 27, "55-59 years / Males": 35, "55-59 years / Persons": 69, "60-64 years / Females": 39, "60-64 years / Males": 28, "60-64 years / Persons": 73, "65-69 years / Females": 33, "65-69 years / Males": 45, "65-69 years / Persons": 75, "70-74 years / Females": 21, "70-74 years / Males": 29, "70-74 years / Persons": 48, "75-79 years / Females": 24, "75-79 years / Males": 17, "75-79 years / Persons": 42, "80-84 years / Females": 7, "80-84 years / Males": 10, "80-84 years / Persons": 23, "85-89 years / Females": 0, "85-89 years / Males": 0, "85-89 years / Persons": 3, "90-94 years / Females": 3, "90-94 years / Males": 0, "90-94 years / Persons": 3, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 2.5, "Average number of persons per bedroom": 0.8, "Median age of persons": 54, "Median mortgage repayment ($/monthly)": 1777, "Median rent ($/weekly)": 350, "Median total family income ($/weekly)": 1593, "Median total household income ($/weekly)": 1481, "Median total personal income ($/weekly)": 653}, "dwellingData": {"postCode": "7162", "Total_NofB_0_i_b": "0", "Total_NofB_1": "10", "Total_NofB_2": "43", "Total_NofB_3": "133", "Total_NofB_4": "54", "Total_NofB_5": "16", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "6", "Total_Total": "263"}, "healthData": {"postCode": "7162", "P_0_cond_Tot": "412", "P_1m_cond_Tot_Tot": "221", "P_2_cond_Tot": "44", "P_3_or_mo_cond_Tot": "16", "P_cond_NS_Tot": "32", "P_Tot_Tot": "667"}, "families": {"postCode": "7162", "over_65": 194, "Tot_P": 667, "CF_ChU15_a_Total_F": 42, "OPF_ChU15_a_Total_F": 15, "Total_F": 221}}