{"populationByPostCode": {"0-4 years / Females": 0, "0-4 years / Males": 3, "0-4 years / Persons": 4, "10-14 years / Females": 3, "10-14 years / Males": 0, "10-14 years / Persons": 3, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 0, "15-19 years / Males": 0, "15-19 years / Persons": 0, "20-24 years / Females": 0, "20-24 years / Males": 0, "20-24 years / Persons": 0, "25-29 years / Females": 0, "25-29 years / Males": 3, "25-29 years / Persons": 4, "30-34 years / Females": 0, "30-34 years / Males": 0, "30-34 years / Persons": 0, "35-39 years / Females": 0, "35-39 years / Males": 0, "35-39 years / Persons": 0, "40-44 years / Females": 5, "40-44 years / Males": 4, "40-44 years / Persons": 9, "45-49 years / Females": 0, "45-49 years / Males": 4, "45-49 years / Persons": 4, "5-9 years / Females": 3, "5-9 years / Males": 0, "5-9 years / Persons": 5, "50-54 years / Females": 3, "50-54 years / Males": 0, "50-54 years / Persons": 9, "55-59 years / Females": 0, "55-59 years / Males": 4, "55-59 years / Persons": 5, "60-64 years / Females": 0, "60-64 years / Males": 0, "60-64 years / Persons": 11, "65-69 years / Females": 3, "65-69 years / Males": 3, "65-69 years / Persons": 9, "70-74 years / Females": 0, "70-74 years / Males": 0, "70-74 years / Persons": 0, "75-79 years / Females": 3, "75-79 years / Males": 8, "75-79 years / Persons": 8, "80-84 years / Females": 0, "80-84 years / Males": 0, "80-84 years / Persons": 0, "85-89 years / Females": 0, "85-89 years / Males": 0, "85-89 years / Persons": 0, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 2, "Average number of persons per bedroom": 0.8, "Median age of persons": 51, "Median mortgage repayment ($/monthly)": 0, "Median rent ($/weekly)": 85, "Median total family income ($/weekly)": 1042, "Median total household income ($/weekly)": 725, "Median total personal income ($/weekly)": 421}, "houseHoldTypes": {"Other tenure type": 5, "Owned outright": 12, "Owned with a mortgage": 0, "Rented: Community housing provider": 7, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 0, "Rented: Person not in same household": 0, "Rented: Real estate agent": 0, "Rented: State or territory housing authority": 4, "Rented: Total": 8, "Tenure type not stated": 6, "Total": 26}, "maritalStatus": {"Divorced": 0, "Married": 11, "Never married": 30, "Separated": 4, "Total": 50, "Widowed": 3}, "dwellingData": {"postCode": "7257", "Total_NofB_0_i_b": "0", "Total_NofB_1": "0", "Total_NofB_2": "7", "Total_NofB_3": "14", "Total_NofB_4": "5", "Total_NofB_5": "0", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "6", "Total_Total": "26"}, "healthData": {"postCode": "7257", "P_0_cond_Tot": "35", "P_1m_cond_Tot_Tot": "31", "P_2_cond_Tot": "3", "P_3_or_mo_cond_Tot": "3", "P_cond_NS_Tot": "4", "P_Tot_Tot": "64"}, "families": {"postCode": "7257", "over_65": 17, "Tot_P": 64, "CF_ChU15_a_Total_F": 3, "OPF_ChU15_a_Total_F": 0, "Total_F": 18}}