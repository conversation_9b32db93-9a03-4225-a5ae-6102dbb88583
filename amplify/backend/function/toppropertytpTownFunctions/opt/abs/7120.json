{"populationByPostCode": {"0-4 years / Females": 30, "0-4 years / Males": 46, "0-4 years / Persons": 74, "10-14 years / Females": 49, "10-14 years / Males": 47, "10-14 years / Persons": 94, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 55, "15-19 years / Males": 61, "15-19 years / Persons": 114, "20-24 years / Females": 53, "20-24 years / Males": 40, "20-24 years / Persons": 92, "25-29 years / Females": 43, "25-29 years / Males": 46, "25-29 years / Persons": 87, "30-34 years / Females": 40, "30-34 years / Males": 30, "30-34 years / Persons": 74, "35-39 years / Females": 40, "35-39 years / Males": 59, "35-39 years / Persons": 94, "40-44 years / Females": 41, "40-44 years / Males": 37, "40-44 years / Persons": 78, "45-49 years / Females": 56, "45-49 years / Males": 49, "45-49 years / Persons": 100, "5-9 years / Females": 50, "5-9 years / Males": 33, "5-9 years / Persons": 78, "50-54 years / Females": 86, "50-54 years / Males": 69, "50-54 years / Persons": 157, "55-59 years / Females": 92, "55-59 years / Males": 84, "55-59 years / Persons": 175, "60-64 years / Females": 86, "60-64 years / Males": 101, "60-64 years / Persons": 192, "65-69 years / Females": 63, "65-69 years / Males": 79, "65-69 years / Persons": 144, "70-74 years / Females": 73, "70-74 years / Males": 78, "70-74 years / Persons": 150, "75-79 years / Females": 40, "75-79 years / Males": 57, "75-79 years / Persons": 95, "80-84 years / Females": 28, "80-84 years / Males": 22, "80-84 years / Persons": 46, "85-89 years / Females": 12, "85-89 years / Males": 15, "85-89 years / Persons": 30, "90-94 years / Females": 9, "90-94 years / Males": 3, "90-94 years / Persons": 12, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "houseHoldTypes": {"Other tenure type": 37, "Owned outright": 367, "Owned with a mortgage": 239, "Rented: Community housing provider": 15, "Rented: Landlord type not stated": 3, "Rented: Other landlord type": 24, "Rented: Person not in same household": 57, "Rented: Real estate agent": 24, "Rented: State or territory housing authority": 11, "Rented: Total": 125, "Tenure type not stated": 21, "Total": 782}, "averagesByPostCode": {"Average household size": 2.2, "Average number of persons per bedroom": 0.7, "Median age of persons": 52, "Median mortgage repayment ($/monthly)": 895, "Median rent ($/weekly)": 188, "Median total family income ($/weekly)": 1365, "Median total household income ($/weekly)": 1027, "Median total personal income ($/weekly)": 531}, "maritalStatus": {"Divorced": 194, "Married": 721, "Never married": 547, "Separated": 66, "Total": 1639, "Widowed": 115}, "dwellingData": {"postCode": "7120", "Total_NofB_0_i_b": "5", "Total_NofB_1": "37", "Total_NofB_2": "152", "Total_NofB_3": "384", "Total_NofB_4": "142", "Total_NofB_5": "36", "Total_NofB_6_or_m": "16", "Total_NofB_NS": "17", "Total_Total": "782"}, "healthData": {"postCode": "7120", "P_0_cond_Tot": "1016", "P_1m_cond_Tot_Tot": "677", "P_2_cond_Tot": "153", "P_3_or_mo_cond_Tot": "93", "P_cond_NS_Tot": "197", "P_Tot_Tot": "1892"}, "families": {"postCode": "7120", "over_65": 477, "Tot_P": 1892, "CF_ChU15_a_Total_F": 97, "OPF_ChU15_a_Total_F": 40, "Total_F": 520}}