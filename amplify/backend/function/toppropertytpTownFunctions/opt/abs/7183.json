{"maritalStatus": {"Divorced": 6, "Married": 14, "Never married": 15, "Separated": 0, "Total": 39, "Widowed": 7}, "populationByPostCode": {"0-4 years / Females": 0, "0-4 years / Males": 0, "0-4 years / Persons": 0, "10-14 years / Females": 0, "10-14 years / Males": 0, "10-14 years / Persons": 0, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 0, "15-19 years / Males": 0, "15-19 years / Persons": 0, "20-24 years / Females": 0, "20-24 years / Males": 0, "20-24 years / Persons": 0, "25-29 years / Females": 0, "25-29 years / Males": 0, "25-29 years / Persons": 0, "30-34 years / Females": 0, "30-34 years / Males": 0, "30-34 years / Persons": 0, "35-39 years / Females": 0, "35-39 years / Males": 0, "35-39 years / Persons": 0, "40-44 years / Females": 0, "40-44 years / Males": 3, "40-44 years / Persons": 3, "45-49 years / Females": 0, "45-49 years / Males": 7, "45-49 years / Persons": 5, "5-9 years / Females": 0, "5-9 years / Males": 0, "5-9 years / Persons": 0, "50-54 years / Females": 5, "50-54 years / Males": 0, "50-54 years / Persons": 6, "55-59 years / Females": 7, "55-59 years / Males": 0, "55-59 years / Persons": 7, "60-64 years / Females": 3, "60-64 years / Males": 9, "60-64 years / Persons": 15, "65-69 years / Females": 0, "65-69 years / Males": 0, "65-69 years / Persons": 8, "70-74 years / Females": 0, "70-74 years / Males": 3, "70-74 years / Persons": 7, "75-79 years / Females": 0, "75-79 years / Males": 0, "75-79 years / Persons": 0, "80-84 years / Females": 0, "80-84 years / Males": 0, "80-84 years / Persons": 0, "85-89 years / Females": 0, "85-89 years / Males": 0, "85-89 years / Persons": 0, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 2, "Average number of persons per bedroom": 0.6, "Median age of persons": 60, "Median mortgage repayment ($/monthly)": 1066, "Median rent ($/weekly)": 295, "Median total family income ($/weekly)": 850, "Median total household income ($/weekly)": 687, "Median total personal income ($/weekly)": 408}, "houseHoldTypes": {"Other tenure type": 0, "Owned outright": 8, "Owned with a mortgage": 7, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 0, "Rented: Person not in same household": 0, "Rented: Real estate agent": 0, "Rented: State or territory housing authority": 0, "Rented: Total": 6, "Tenure type not stated": 0, "Total": 22}, "dwellingData": {"postCode": "7183", "Total_NofB_0_i_b": "0", "Total_NofB_1": "0", "Total_NofB_2": "6", "Total_NofB_3": "13", "Total_NofB_4": "4", "Total_NofB_5": "0", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "0", "Total_Total": "22"}, "healthData": {"postCode": "7183", "P_0_cond_Tot": "19", "P_1m_cond_Tot_Tot": "19", "P_2_cond_Tot": "3", "P_3_or_mo_cond_Tot": "6", "P_cond_NS_Tot": "5", "P_Tot_Tot": "42"}, "families": {"postCode": "7183", "over_65": 15, "Tot_P": 42, "CF_ChU15_a_Total_F": 0, "OPF_ChU15_a_Total_F": 0, "Total_F": 16}}