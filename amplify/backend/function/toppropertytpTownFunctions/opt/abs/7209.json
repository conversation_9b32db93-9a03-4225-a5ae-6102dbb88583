{"maritalStatus": {"Divorced": 48, "Married": 187, "Never married": 100, "Separated": 16, "Total": 376, "Widowed": 21}, "populationByPostCode": {"0-4 years / Females": 8, "0-4 years / Males": 6, "0-4 years / Persons": 16, "10-14 years / Females": 9, "10-14 years / Males": 11, "10-14 years / Persons": 21, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 0, "15-19 years / Males": 10, "15-19 years / Persons": 12, "20-24 years / Females": 8, "20-24 years / Males": 5, "20-24 years / Persons": 17, "25-29 years / Females": 5, "25-29 years / Males": 8, "25-29 years / Persons": 13, "30-34 years / Females": 8, "30-34 years / Males": 9, "30-34 years / Persons": 14, "35-39 years / Females": 6, "35-39 years / Males": 8, "35-39 years / Persons": 14, "40-44 years / Females": 6, "40-44 years / Males": 4, "40-44 years / Persons": 10, "45-49 years / Females": 14, "45-49 years / Males": 12, "45-49 years / Persons": 25, "5-9 years / Females": 4, "5-9 years / Males": 7, "5-9 years / Persons": 7, "50-54 years / Females": 17, "50-54 years / Males": 16, "50-54 years / Persons": 35, "55-59 years / Females": 22, "55-59 years / Males": 24, "55-59 years / Persons": 46, "60-64 years / Females": 22, "60-64 years / Males": 17, "60-64 years / Persons": 38, "65-69 years / Females": 27, "65-69 years / Males": 23, "65-69 years / Persons": 46, "70-74 years / Females": 26, "70-74 years / Males": 28, "70-74 years / Persons": 53, "75-79 years / Females": 7, "75-79 years / Males": 18, "75-79 years / Persons": 21, "80-84 years / Females": 10, "80-84 years / Males": 8, "80-84 years / Persons": 16, "85-89 years / Females": 5, "85-89 years / Males": 4, "85-89 years / Persons": 11, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "houseHoldTypes": {"Other tenure type": 16, "Owned outright": 94, "Owned with a mortgage": 32, "Rented: Community housing provider": 3, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 11, "Rented: Person not in same household": 22, "Rented: Real estate agent": 4, "Rented: State or territory housing authority": 0, "Rented: Total": 36, "Tenure type not stated": 3, "Total": 185}, "averagesByPostCode": {"Average household size": 2, "Average number of persons per bedroom": 0.7, "Median age of persons": 57, "Median mortgage repayment ($/monthly)": 867, "Median rent ($/weekly)": 200, "Median total family income ($/weekly)": 1196, "Median total household income ($/weekly)": 903, "Median total personal income ($/weekly)": 539}, "dwellingData": {"postCode": "7209", "Total_NofB_0_i_b": "0", "Total_NofB_1": "14", "Total_NofB_2": "43", "Total_NofB_3": "86", "Total_NofB_4": "34", "Total_NofB_5": "9", "Total_NofB_6_or_m": "7", "Total_NofB_NS": "0", "Total_Total": "185"}, "healthData": {"postCode": "7209", "P_0_cond_Tot": "227", "P_1m_cond_Tot_Tot": "139", "P_2_cond_Tot": "30", "P_3_or_mo_cond_Tot": "15", "P_cond_NS_Tot": "49", "P_Tot_Tot": "414"}, "families": {"postCode": "7209", "over_65": 147, "Tot_P": 414, "CF_ChU15_a_Total_F": 12, "OPF_ChU15_a_Total_F": 6, "Total_F": 122}}