{"maritalStatus": {"Divorced": 99, "Married": 397, "Never married": 293, "Separated": 51, "Total": 919, "Widowed": 83}, "populationByPostCode": {"0-4 years / Females": 20, "0-4 years / Males": 37, "0-4 years / Persons": 61, "10-14 years / Females": 26, "10-14 years / Males": 23, "10-14 years / Persons": 56, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 30, "15-19 years / Males": 27, "15-19 years / Persons": 59, "20-24 years / Females": 25, "20-24 years / Males": 23, "20-24 years / Persons": 48, "25-29 years / Females": 34, "25-29 years / Males": 20, "25-29 years / Persons": 57, "30-34 years / Females": 16, "30-34 years / Males": 26, "30-34 years / Persons": 45, "35-39 years / Females": 24, "35-39 years / Males": 23, "35-39 years / Persons": 46, "40-44 years / Females": 26, "40-44 years / Males": 15, "40-44 years / Persons": 41, "45-49 years / Females": 39, "45-49 years / Males": 29, "45-49 years / Persons": 73, "5-9 years / Females": 21, "5-9 years / Males": 29, "5-9 years / Persons": 53, "50-54 years / Females": 29, "50-54 years / Males": 39, "50-54 years / Persons": 68, "55-59 years / Females": 39, "55-59 years / Males": 37, "55-59 years / Persons": 77, "60-64 years / Females": 54, "60-64 years / Males": 30, "60-64 years / Persons": 85, "65-69 years / Females": 43, "65-69 years / Males": 54, "65-69 years / Persons": 94, "70-74 years / Females": 51, "70-74 years / Males": 48, "70-74 years / Persons": 96, "75-79 years / Females": 27, "75-79 years / Males": 33, "75-79 years / Persons": 58, "80-84 years / Females": 18, "80-84 years / Males": 16, "80-84 years / Persons": 34, "85-89 years / Females": 22, "85-89 years / Males": 8, "85-89 years / Persons": 32, "90-94 years / Females": 10, "90-94 years / Males": 0, "90-94 years / Persons": 10, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 2.1, "Average number of persons per bedroom": 0.7, "Median age of persons": 51, "Median mortgage repayment ($/monthly)": 867, "Median rent ($/weekly)": 215, "Median total family income ($/weekly)": 1190, "Median total household income ($/weekly)": 852, "Median total personal income ($/weekly)": 509}, "houseHoldTypes": {"Other tenure type": 28, "Owned outright": 217, "Owned with a mortgage": 111, "Rented: Community housing provider": 9, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 14, "Rented: Person not in same household": 33, "Rented: Real estate agent": 26, "Rented: State or territory housing authority": 14, "Rented: Total": 100, "Tenure type not stated": 12, "Total": 466}, "dwellingData": {"postCode": "7210", "Total_NofB_0_i_b": "4", "Total_NofB_1": "21", "Total_NofB_2": "119", "Total_NofB_3": "234", "Total_NofB_4": "50", "Total_NofB_5": "11", "Total_NofB_6_or_m": "11", "Total_NofB_NS": "10", "Total_Total": "466"}, "healthData": {"postCode": "7210", "P_0_cond_Tot": "573", "P_1m_cond_Tot_Tot": "402", "P_2_cond_Tot": "100", "P_3_or_mo_cond_Tot": "83", "P_cond_NS_Tot": "103", "P_Tot_Tot": "1076"}, "families": {"postCode": "7210", "over_65": 324, "Tot_P": 1076, "CF_ChU15_a_Total_F": 55, "OPF_ChU15_a_Total_F": 24, "Total_F": 286}}