{"populationByPostCode": {"0-4 years / Females": 289, "0-4 years / Males": 340, "0-4 years / Persons": 629, "10-14 years / Females": 385, "10-14 years / Males": 430, "10-14 years / Persons": 818, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 341, "15-19 years / Males": 378, "15-19 years / Persons": 723, "20-24 years / Females": 340, "20-24 years / Males": 355, "20-24 years / Persons": 690, "25-29 years / Females": 307, "25-29 years / Males": 328, "25-29 years / Persons": 639, "30-34 years / Females": 374, "30-34 years / Males": 341, "30-34 years / Persons": 719, "35-39 years / Females": 384, "35-39 years / Males": 364, "35-39 years / Persons": 753, "40-44 years / Females": 342, "40-44 years / Males": 352, "40-44 years / Persons": 696, "45-49 years / Females": 364, "45-49 years / Males": 381, "45-49 years / Persons": 753, "5-9 years / Females": 360, "5-9 years / Males": 343, "5-9 years / Persons": 701, "50-54 years / Females": 434, "50-54 years / Males": 394, "50-54 years / Persons": 826, "55-59 years / Females": 416, "55-59 years / Males": 465, "55-59 years / Persons": 882, "60-64 years / Females": 431, "60-64 years / Males": 462, "60-64 years / Persons": 890, "65-69 years / Females": 387, "65-69 years / Males": 352, "65-69 years / Persons": 737, "70-74 years / Females": 314, "70-74 years / Males": 370, "70-74 years / Persons": 688, "75-79 years / Females": 202, "75-79 years / Males": 213, "75-79 years / Persons": 418, "80-84 years / Females": 164, "80-84 years / Males": 139, "80-84 years / Persons": 303, "85-89 years / Females": 79, "85-89 years / Males": 73, "85-89 years / Persons": 153, "90-94 years / Females": 31, "90-94 years / Males": 14, "90-94 years / Persons": 43, "95-99 years / Females": 11, "95-99 years / Males": 3, "95-99 years / Persons": 15}, "averagesByPostCode": {"Average household size": 2.4, "Average number of persons per bedroom": 0.8, "Median age of persons": 42, "Median mortgage repayment ($/monthly)": 1192, "Median rent ($/weekly)": 270, "Median total family income ($/weekly)": 1485, "Median total household income ($/weekly)": 1197, "Median total personal income ($/weekly)": 607}, "houseHoldTypes": {"Other tenure type": 101, "Owned outright": 1676, "Owned with a mortgage": 1743, "Rented: Community housing provider": 35, "Rented: Landlord type not stated": 11, "Rented: Other landlord type": 76, "Rented: Person not in same household": 352, "Rented: Real estate agent": 346, "Rented: State or territory housing authority": 179, "Rented: Total": 993, "Tenure type not stated": 65, "Total": 4582}, "maritalStatus": {"Divorced": 1033, "Married": 4007, "Never married": 3870, "Separated": 425, "Total": 9919, "Widowed": 577}, "dwellingData": {"postCode": "7140", "Total_NofB_0_i_b": "37", "Total_NofB_1": "200", "Total_NofB_2": "828", "Total_NofB_3": "2484", "Total_NofB_4": "750", "Total_NofB_5": "154", "Total_NofB_6_or_m": "46", "Total_NofB_NS": "75", "Total_Total": "4582"}, "healthData": {"postCode": "7140", "P_0_cond_Tot": "6628", "P_1m_cond_Tot_Tot": "4316", "P_2_cond_Tot": "996", "P_3_or_mo_cond_Tot": "573", "P_cond_NS_Tot": "1129", "P_Tot_Tot": "12069"}, "families": {"postCode": "7140", "over_65": 2357, "Tot_P": 12069, "CF_ChU15_a_Total_F": 776, "OPF_ChU15_a_Total_F": 352, "Total_F": 3273}}