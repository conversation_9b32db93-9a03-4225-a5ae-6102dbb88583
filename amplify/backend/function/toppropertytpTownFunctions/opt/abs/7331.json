{"maritalStatus": {"Divorced": 51, "Married": 261, "Never married": 155, "Separated": 20, "Total": 507, "Widowed": 24}, "averagesByPostCode": {"Average household size": 2.1, "Average number of persons per bedroom": 0.7, "Median age of persons": 51, "Median mortgage repayment ($/monthly)": 1350, "Median rent ($/weekly)": 225, "Median total family income ($/weekly)": 1762, "Median total household income ($/weekly)": 1351, "Median total personal income ($/weekly)": 742}, "populationByPostCode": {"0-4 years / Females": 15, "0-4 years / Males": 15, "0-4 years / Persons": 26, "10-14 years / Females": 16, "10-14 years / Males": 14, "10-14 years / Persons": 26, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 15, "15-19 years / Males": 12, "15-19 years / Persons": 24, "20-24 years / Females": 8, "20-24 years / Males": 14, "20-24 years / Persons": 19, "25-29 years / Females": 9, "25-29 years / Males": 19, "25-29 years / Persons": 25, "30-34 years / Females": 16, "30-34 years / Males": 12, "30-34 years / Persons": 30, "35-39 years / Females": 17, "35-39 years / Males": 9, "35-39 years / Persons": 31, "40-44 years / Females": 14, "40-44 years / Males": 20, "40-44 years / Persons": 40, "45-49 years / Females": 16, "45-49 years / Males": 12, "45-49 years / Persons": 34, "5-9 years / Females": 14, "5-9 years / Males": 13, "5-9 years / Persons": 28, "50-54 years / Females": 22, "50-54 years / Males": 15, "50-54 years / Persons": 38, "55-59 years / Females": 29, "55-59 years / Males": 19, "55-59 years / Persons": 49, "60-64 years / Females": 29, "60-64 years / Males": 40, "60-64 years / Persons": 71, "65-69 years / Females": 24, "65-69 years / Males": 33, "65-69 years / Persons": 55, "70-74 years / Females": 19, "70-74 years / Males": 25, "70-74 years / Persons": 44, "75-79 years / Females": 14, "75-79 years / Males": 15, "75-79 years / Persons": 33, "80-84 years / Females": 13, "80-84 years / Males": 9, "80-84 years / Persons": 13, "85-89 years / Females": 0, "85-89 years / Males": 10, "85-89 years / Persons": 9, "90-94 years / Females": 0, "90-94 years / Males": 0, "90-94 years / Persons": 0, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "houseHoldTypes": {"Other tenure type": 5, "Owned outright": 116, "Owned with a mortgage": 70, "Rented: Community housing provider": 4, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 4, "Rented: Person not in same household": 28, "Rented: Real estate agent": 16, "Rented: State or territory housing authority": 7, "Rented: Total": 62, "Tenure type not stated": 6, "Total": 254}, "dwellingData": {"postCode": "7331", "Total_NofB_0_i_b": "0", "Total_NofB_1": "9", "Total_NofB_2": "58", "Total_NofB_3": "137", "Total_NofB_4": "31", "Total_NofB_5": "9", "Total_NofB_6_or_m": "0", "Total_NofB_NS": "9", "Total_Total": "254"}, "healthData": {"postCode": "7331", "P_0_cond_Tot": "366", "P_1m_cond_Tot_Tot": "181", "P_2_cond_Tot": "37", "P_3_or_mo_cond_Tot": "20", "P_cond_NS_Tot": "47", "P_Tot_Tot": "595"}, "families": {"postCode": "7331", "over_65": 154, "Tot_P": 595, "CF_ChU15_a_Total_F": 35, "OPF_ChU15_a_Total_F": 9, "Total_F": 164}}