{"populationByPostCode": {"0-4 years / Females": 41, "0-4 years / Males": 54, "0-4 years / Persons": 92, "10-14 years / Females": 42, "10-14 years / Males": 48, "10-14 years / Persons": 85, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 21, "15-19 years / Males": 31, "15-19 years / Persons": 51, "20-24 years / Females": 28, "20-24 years / Males": 24, "20-24 years / Persons": 52, "25-29 years / Females": 40, "25-29 years / Males": 52, "25-29 years / Persons": 92, "30-34 years / Females": 49, "30-34 years / Males": 59, "30-34 years / Persons": 111, "35-39 years / Females": 58, "35-39 years / Males": 56, "35-39 years / Persons": 112, "40-44 years / Females": 43, "40-44 years / Males": 59, "40-44 years / Persons": 104, "45-49 years / Females": 29, "45-49 years / Males": 43, "45-49 years / Persons": 70, "5-9 years / Females": 49, "5-9 years / Males": 42, "5-9 years / Persons": 89, "50-54 years / Females": 57, "50-54 years / Males": 60, "50-54 years / Persons": 117, "55-59 years / Females": 72, "55-59 years / Males": 58, "55-59 years / Persons": 132, "60-64 years / Females": 52, "60-64 years / Males": 58, "60-64 years / Persons": 108, "65-69 years / Females": 71, "65-69 years / Males": 64, "65-69 years / Persons": 131, "70-74 years / Females": 52, "70-74 years / Males": 53, "70-74 years / Persons": 104, "75-79 years / Females": 29, "75-79 years / Males": 36, "75-79 years / Persons": 61, "80-84 years / Females": 26, "80-84 years / Males": 34, "80-84 years / Persons": 61, "85-89 years / Females": 15, "85-89 years / Males": 7, "85-89 years / Persons": 24, "90-94 years / Females": 6, "90-94 years / Males": 5, "90-94 years / Persons": 12, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 5}, "maritalStatus": {"Divorced": 132, "Married": 602, "Never married": 473, "Separated": 48, "Total": 1341, "Widowed": 81}, "houseHoldTypes": {"Other tenure type": 37, "Owned outright": 283, "Owned with a mortgage": 157, "Rented: Community housing provider": 10, "Rented: Landlord type not stated": 6, "Rented: Other landlord type": 32, "Rented: Person not in same household": 85, "Rented: Real estate agent": 18, "Rented: State or territory housing authority": 11, "Rented: Total": 161, "Tenure type not stated": 16, "Total": 652}, "averagesByPostCode": {"Average household size": 2.1, "Average number of persons per bedroom": 0.7, "Median age of persons": 45, "Median mortgage repayment ($/monthly)": 1083, "Median rent ($/weekly)": 190, "Median total family income ($/weekly)": 1719, "Median total household income ($/weekly)": 1330, "Median total personal income ($/weekly)": 845}, "dwellingData": {"postCode": "7256", "Total_NofB_0_i_b": "6", "Total_NofB_1": "42", "Total_NofB_2": "115", "Total_NofB_3": "344", "Total_NofB_4": "113", "Total_NofB_5": "18", "Total_NofB_6_or_m": "6", "Total_NofB_NS": "9", "Total_Total": "652"}, "healthData": {"postCode": "7256", "P_0_cond_Tot": "937", "P_1m_cond_Tot_Tot": "465", "P_2_cond_Tot": "118", "P_3_or_mo_cond_Tot": "54", "P_cond_NS_Tot": "212", "P_Tot_Tot": "1617"}, "families": {"postCode": "7256", "over_65": 398, "Tot_P": 1617, "CF_ChU15_a_Total_F": 108, "OPF_ChU15_a_Total_F": 20, "Total_F": 401}}