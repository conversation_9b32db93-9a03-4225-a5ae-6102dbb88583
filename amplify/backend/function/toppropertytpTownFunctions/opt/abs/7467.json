{"maritalStatus": {"Divorced": 165, "Married": 525, "Never married": 636, "Separated": 60, "Total": 1512, "Widowed": 130}, "populationByPostCode": {"0-4 years / Females": 49, "0-4 years / Males": 52, "0-4 years / Persons": 105, "10-14 years / Females": 41, "10-14 years / Males": 42, "10-14 years / Persons": 82, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 32, "15-19 years / Males": 53, "15-19 years / Persons": 80, "20-24 years / Females": 47, "20-24 years / Males": 47, "20-24 years / Persons": 97, "25-29 years / Females": 56, "25-29 years / Males": 48, "25-29 years / Persons": 105, "30-34 years / Females": 58, "30-34 years / Males": 32, "30-34 years / Persons": 86, "35-39 years / Females": 33, "35-39 years / Males": 50, "35-39 years / Persons": 85, "40-44 years / Females": 46, "40-44 years / Males": 42, "40-44 years / Persons": 87, "45-49 years / Females": 58, "45-49 years / Males": 59, "45-49 years / Persons": 122, "5-9 years / Females": 48, "5-9 years / Males": 56, "5-9 years / Persons": 108, "50-54 years / Females": 73, "50-54 years / Males": 91, "50-54 years / Persons": 166, "55-59 years / Females": 61, "55-59 years / Males": 72, "55-59 years / Persons": 132, "60-64 years / Females": 67, "60-64 years / Males": 80, "60-64 years / Persons": 148, "65-69 years / Females": 62, "65-69 years / Males": 83, "65-69 years / Persons": 146, "70-74 years / Females": 53, "70-74 years / Males": 56, "70-74 years / Persons": 116, "75-79 years / Females": 34, "75-79 years / Males": 34, "75-79 years / Persons": 65, "80-84 years / Females": 27, "80-84 years / Males": 21, "80-84 years / Persons": 49, "85-89 years / Females": 10, "85-89 years / Males": 5, "85-89 years / Persons": 16, "90-94 years / Females": 9, "90-94 years / Males": 6, "90-94 years / Persons": 7, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 2, "Average number of persons per bedroom": 0.7, "Median age of persons": 47, "Median mortgage repayment ($/monthly)": 574, "Median rent ($/weekly)": 150, "Median total family income ($/weekly)": 1371, "Median total household income ($/weekly)": 851, "Median total personal income ($/weekly)": 509}, "houseHoldTypes": {"Other tenure type": 30, "Owned outright": 401, "Owned with a mortgage": 183, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 4, "Rented: Other landlord type": 28, "Rented: Person not in same household": 65, "Rented: Real estate agent": 77, "Rented: State or territory housing authority": 13, "Rented: Total": 185, "Tenure type not stated": 14, "Total": 816}, "dwellingData": {"postCode": "7467", "Total_NofB_0_i_b": "5", "Total_NofB_1": "34", "Total_NofB_2": "152", "Total_NofB_3": "492", "Total_NofB_4": "109", "Total_NofB_5": "13", "Total_NofB_6_or_m": "6", "Total_NofB_NS": "19", "Total_Total": "816"}, "healthData": {"postCode": "7467", "P_0_cond_Tot": "993", "P_1m_cond_Tot_Tot": "638", "P_2_cond_Tot": "153", "P_3_or_mo_cond_Tot": "101", "P_cond_NS_Tot": "175", "P_Tot_Tot": "1808"}, "families": {"postCode": "7467", "over_65": 399, "Tot_P": 1808, "CF_ChU15_a_Total_F": 97, "OPF_ChU15_a_Total_F": 47, "Total_F": 453}}