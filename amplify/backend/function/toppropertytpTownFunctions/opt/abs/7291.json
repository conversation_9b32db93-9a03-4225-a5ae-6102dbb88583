{"populationByPostCode": {"0-4 years / Females": 25, "0-4 years / Males": 33, "0-4 years / Persons": 59, "10-14 years / Females": 21, "10-14 years / Males": 28, "10-14 years / Persons": 51, "100 years and over / Females": 0, "100 years and over / Males": 0, "100 years and over / Persons": 0, "15-19 years / Females": 12, "15-19 years / Males": 21, "15-19 years / Persons": 38, "20-24 years / Females": 17, "20-24 years / Males": 16, "20-24 years / Persons": 36, "25-29 years / Females": 25, "25-29 years / Males": 21, "25-29 years / Persons": 41, "30-34 years / Females": 28, "30-34 years / Males": 27, "30-34 years / Persons": 52, "35-39 years / Females": 26, "35-39 years / Males": 24, "35-39 years / Persons": 49, "40-44 years / Females": 21, "40-44 years / Males": 18, "40-44 years / Persons": 41, "45-49 years / Females": 23, "45-49 years / Males": 19, "45-49 years / Persons": 47, "5-9 years / Females": 18, "5-9 years / Males": 21, "5-9 years / Persons": 39, "50-54 years / Females": 25, "50-54 years / Males": 24, "50-54 years / Persons": 56, "55-59 years / Females": 21, "55-59 years / Males": 23, "55-59 years / Persons": 45, "60-64 years / Females": 24, "60-64 years / Males": 25, "60-64 years / Persons": 48, "65-69 years / Females": 22, "65-69 years / Males": 20, "65-69 years / Persons": 39, "70-74 years / Females": 21, "70-74 years / Males": 10, "70-74 years / Persons": 32, "75-79 years / Females": 11, "75-79 years / Males": 19, "75-79 years / Persons": 27, "80-84 years / Females": 10, "80-84 years / Males": 6, "80-84 years / Persons": 10, "85-89 years / Females": 3, "85-89 years / Males": 3, "85-89 years / Persons": 9, "90-94 years / Females": 3, "90-94 years / Males": 3, "90-94 years / Persons": 3, "95-99 years / Females": 0, "95-99 years / Males": 0, "95-99 years / Persons": 0}, "averagesByPostCode": {"Average household size": 2.6, "Average number of persons per bedroom": 0.8, "Median age of persons": 39, "Median mortgage repayment ($/monthly)": 1517, "Median rent ($/weekly)": 278, "Median total family income ($/weekly)": 2071, "Median total household income ($/weekly)": 1701, "Median total personal income ($/weekly)": 783}, "houseHoldTypes": {"Other tenure type": 9, "Owned outright": 100, "Owned with a mortgage": 122, "Rented: Community housing provider": 0, "Rented: Landlord type not stated": 0, "Rented: Other landlord type": 0, "Rented: Person not in same household": 29, "Rented: Real estate agent": 5, "Rented: State or territory housing authority": 0, "Rented: Total": 41, "Tenure type not stated": 10, "Total": 271}, "maritalStatus": {"Divorced": 47, "Married": 278, "Never married": 195, "Separated": 25, "Total": 573, "Widowed": 25}, "dwellingData": {"postCode": "7291", "Total_NofB_0_i_b": "0", "Total_NofB_1": "11", "Total_NofB_2": "36", "Total_NofB_3": "127", "Total_NofB_4": "67", "Total_NofB_5": "26", "Total_NofB_6_or_m": "3", "Total_NofB_NS": "12", "Total_Total": "271"}, "healthData": {"postCode": "7291", "P_0_cond_Tot": "456", "P_1m_cond_Tot_Tot": "234", "P_2_cond_Tot": "53", "P_3_or_mo_cond_Tot": "34", "P_cond_NS_Tot": "36", "P_Tot_Tot": "721"}, "families": {"postCode": "7291", "over_65": 120, "Tot_P": 721, "CF_ChU15_a_Total_F": 78, "OPF_ChU15_a_Total_F": 12, "Total_F": 214}}