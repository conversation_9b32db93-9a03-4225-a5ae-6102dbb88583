{"version": 1, "paths": {"/townData": {"name": "/townData", "lambdaFunction": "tpTownData", "permissions": {"setting": "open"}}, "/sustainable100": {"name": "/sustainable100", "lambdaFunction": "tpSustainable100", "permissions": {"setting": "open"}}, "/addressPrediction": {"name": "/addressPrediction", "lambdaFunction": "tpAddressPrediction", "permissions": {"setting": "open"}}, "/address": {"name": "/address", "lambdaFunction": "tpAddress", "permissions": {"setting": "open"}}, "/airconData": {"name": "/airconData", "lambdaFunction": "tpAirconData", "permissions": {"setting": "open"}}, "/heatingData": {"name": "/heatingData", "lambdaFunction": "tpHeatingData", "permissions": {"setting": "open"}}, "/airconChatBot": {"name": "/airconChatBot", "lambdaFunction": "tpAirconChatBot", "permissions": {"setting": "open"}}, "/chatbotData": {"name": "/chatbotData", "lambdaFunction": "tpChatbotData", "permissions": {"setting": "open"}}, "/testing": {"name": "/testing", "lambdaFunction": "testingAPI", "permissions": {"setting": "open"}}, "/ev": {"name": "/ev", "lambdaFunction": "tpEvData", "permissions": {"setting": "open"}}}}