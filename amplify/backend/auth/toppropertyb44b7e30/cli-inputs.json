{"version": "1", "cognitoConfig": {"identityPoolName": "toppropertyb44b7e30_identitypool_b44b7e30", "allowUnauthenticatedIdentities": true, "resourceNameTruncated": "topprob44b7e30", "userPoolName": "toppropertyb44b7e30_userpool_b44b7e30", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "topprob44b7e30_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "b44b7e30", "resourceName": "toppropertyb44b7e30", "authSelections": "identityPoolAndUserPool", "useDefault": "default", "usernameAttributes": ["email"], "triggers": {}, "userPoolGroupList": ["admin", "server"], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true, "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "breakCircularDependency": true, "dependsOn": [], "parentStack": {"Ref": "AWS::StackId"}, "permissions": []}}